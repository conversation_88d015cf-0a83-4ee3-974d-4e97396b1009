/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     app_func.h
 *  @brief    Application function declarations for NFC lock firmware
 *
 *  This module contains all app_func_0 through app_func_15 function declarations
 *  used by the mailbox system for various application functionalities.
 */

#ifndef _APP_FUNC_H_
#define _APP_FUNC_H_

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Application function 0
 */
extern void app_func_0(void);

/**
 * @brief Application function 1
 */
extern void app_func_1(void);

/**
 * @brief Application function 2
 */
extern void app_func_2(void);

/**
 * @brief Application function 3
 */
extern void app_func_3(void);

/**
 * @brief Application function 4
 */
extern void app_func_4(void);

/**
 * @brief Application function 5
 */
extern void app_func_5(void);

/**
 * @brief Application function 6
 */
extern void app_func_6(void);

/**
 * @brief Application function 7
 */
extern void app_func_7(void);

/**
 * @brief Application function 8
 */
extern void app_func_8(void);

/**
 * @brief Application function 9
 */
extern void app_func_9(void);

/**
 * @brief Application function 10
 */
extern void app_func_10(void);

/**
 * @brief Application function 11
 */
extern void app_func_11(void);

/**
 * @brief Application function 12
 */
extern void app_func_12(void);

/**
 * @brief Application function 13
 */
extern void app_func_13(void);

/**
 * @brief Application function 14
 */
extern void app_func_14(void);

/**
 * @brief Application function 15
 */
extern void app_func_15(void);

#ifdef __cplusplus
}
#endif

#endif /* _APP_FUNC_H_ */
