/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     my_nvm.h
 *  @brief    Custom configuration storage for NFC lock firmware
 */

/* lint -save -e960 */

#ifndef _MY_NVM_H_
#define _MY_NVM_H_

#include <stdint.h>
#include <stdbool.h>

/** @addtogroup Infineon
 * @{
 */

/** @addtogroup My_nvm
 * @{
 */

/** @addtogroup my_config
 * @{
 */

/**
 * @brief Custom configuration structure with 8 uint32_t values
 */
typedef struct my_config_s
{
    uint32_t value1;        //!< Configuration value 1
    uint32_t value2;        //!< Configuration value 2
    uint32_t value3;        //!< Configuration value 3
    uint32_t value4;        //!< Configuration value 4
    uint32_t value5;        //!< Configuration value 5
    uint32_t value6;        //!< Configuration value 6
    uint32_t value7;        //!< Configuration value 7
    uint32_t value8;        //!< Configuration value 8
} my_config_t;

extern my_config_t my_config;

/**
 * @brief Load my configuration from NVM into RAM
 *
 * @return true: success; false: configuration was not loaded, default data is used
 */
extern bool my_config_load(void);

/**
 * @brief Save my configuration in NVM
 */
extern void my_config_save(void);

/**
 * @brief Set my configuration data in RAM to defaults
 */
extern void my_config_set_default(void);

/**
 * @brief Clear my configuration from NVM
 */
extern void my_config_clear(void);

/**
 * @brief Check if my configuration sensor is enabled
 */
extern bool my_config_is_sensor_enabled(void);

/**
 * @brief Get my configuration sensor values
 */
extern void my_config_get_sensor_values(uint32_t *x, uint32_t *y, uint32_t *z);

/**
 * @brief Enable my configuration sensor
 */
extern void my_config_enable_sensor(uint32_t x, uint32_t y, uint32_t z);

/**
 * @brief Disable my configuration sensor
 */
extern void my_config_disable_sensor(void);

/** @} */ /* End of group my_config */

/** @} */ /* End of group My_nvm */

/** @} */ /* End of group Infineon */

#endif /* _MY_NVM_H_ */
