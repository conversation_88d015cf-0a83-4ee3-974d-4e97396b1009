/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     app_func.c
 *  @brief    Application function implementations for NFC lock firmware
 *
 *  This module contains all app_func_0 through app_func_15 function implementations
 *  used by the mailbox system for various application functionalities.
 */

// standard libs
#include "core_cm0.h"
#include <stdbool.h>
#include <string.h>
#include <stdlib.h>

// Smack ROM lib
#include "rom_lib.h"

// Smack NVM lib
#include "smack_exchange.h"

// Smack stepwise project
#include "smack_dand_data.h"

// Project includes
#include "util.h"
#include "my_nvm.h"
#include "challenge.h"
#include "sensor_util.h"
#include "app_func.h"

/**
 * @brief Generic application function implementation
 *
 * This function provides a standard response pattern for generic app functions.
 * It sets up the mailbox with an ID pattern and counter.
 *
 * @param id The function ID to be written to mailbox
 */
static void app_func_generic(uint32_t id)
{
  Mailbox_t *mbox;
  static uint32_t counter = 0;
  mbox = get_mailbox_address();
  mbox->content[0] = id;
  mbox->content[1] = 0;
  mbox->content[2] = counter++;
  mbox->content[3] = id;
}

void app_func_0(void)
{
  // app_func_generic(0x00000000);

  if (challenge_is_passed())
  {
    smack_exchange_handler();
  }
}

void app_func_1(void)
{
  // app_func_generic(0x11111111);

  challenge_validate_response();
}

void app_func_2(void)
{
  // app_func_generic(0x22222222);

  challenge_generate_value();
}

void app_func_3(void)
{
  // app_func_generic(0x33333333);

  if (challenge_is_passed())
  {
    Mailbox_t *p;
    p = get_mailbox_address();

    my_config_enable_sensor(p->content[1], p->content[2], p->content[3]);
  }
}

void app_func_4(void)
{
  app_func_generic(0x44444444);
}

void app_func_5(void)
{
  app_func_generic(0x55555555);
}

void app_func_6(void)
{
  app_func_generic(0x66666666);
}

void app_func_7(void)
{
  app_func_generic(0x77777777);
}

void app_func_8(void)
{
  app_func_generic(0x88888888);
}

void app_func_9(void)
{
  app_func_generic(0x99999999);
}

void app_func_10(void)
{
  app_func_generic(0xaaaaaaaa);
}

void app_func_11(void)
{
  // app_func_generic(0xbbbbbbbb);

  if (challenge_is_passed())
  {
    Mailbox_t *p;
    p = get_mailbox_address();
    bool su_key_match = true;
    const uint32_t *stored_su_key = config_device.su_key.w;

    for (int i = 0; i < 4; i++)
    {
      if (p->content[i + 1] != htonl(stored_su_key[i]))
      {
        su_key_match = false;
        break;
      }
    }

    if (su_key_match)
    {
      session.erase |= 0x01;
      mailbox_write_string(0, "ok");
    }
    else
    {
      mailbox_write_string(0, "fail");
    }
  }
}

void app_func_12(void)
{
  // app_func_generic(0xcccccccc);

  if (challenge_is_passed())
  {
    Mailbox_t *p;
    p = get_mailbox_address();
    bool field_present;
    field_present = check_rf_field();
    if (field_present)
    {
      uint32_t rssi_raw, vccca_raw;
      rssi_raw = message_get_rssi(p);
      vccca_raw = message_get_vccca(p);
      p->content[0] = rssi_raw;
      p->content[1] = vccca_raw;
    }
    else
    {
      p->content[0] = 0;
      p->content[1] = 0;
    }
  }
}

void app_func_13(void)
{
  // app_func_generic(0xdddddddd);

  if (challenge_is_passed())
  {
    Mailbox_t *p;
    p = get_mailbox_address();

    int16_t x, y, z;
    uint16_t t;
    get_sensor_value(&x, &y, &z, &t);
    bool reached = is_target_reached();
    p->content[0] = x;
    p->content[1] = y;
    p->content[2] = z;
    p->content[3] = t;
    p->content[4] = reached;
  }
}

void app_func_14(void)
{
  app_func_generic(0xeeeeeeee);
}

void app_func_15(void)
{
  app_func_generic(0xffffffff);
}
