#include "sensor_util.h"

void get_sensor_value(int16_t *x, int16_t *y, int16_t *z, uint16_t *t)
{
  sensor_init_sf();
  sensor_read_sf(x, y, z, t);
}

bool is_target_reached()
{
  int16_t sensor_x, sensor_y, sensor_z;
  uint16_t sensor_t;
  get_sensor_value(&sensor_x, &sensor_y, &sensor_z, &sensor_t);

  uint32_t target_x, target_y, target_z;
  my_config_get_sensor_values(&target_x, &target_y, &target_z);
  int16_t target_x_from, target_x_to, target_y_from, target_y_to, target_z_from, target_z_to;
  target_x_from = target_x >> 16;
  target_x_to = target_x & 0xffff;
  target_y_from = target_y >> 16;
  target_y_to = target_y & 0xffff;
  target_z_from = target_z >> 16;
  target_z_to = target_z & 0xffff;

  if (my_config_is_sensor_enabled())
  {
    return (
        abs(sensor_x) >= target_x_from &&
        abs(sensor_x) <= target_x_to &&
        abs(sensor_y) >= target_y_from &&
        abs(sensor_y) <= target_y_to &&
        abs(sensor_z) >= target_z_from &&
        abs(sensor_z) <= target_z_to);
  }
  else
  {
    return true;
  }
}
