/* ============================================================================
** Copyright (c) 2021 Infineon Technologies AG
**               All rights reserved.
**               www.infineon.com
** ============================================================================
**
** ============================================================================
** Redistribution and use of this software only permitted to the extent
** expressly agreed with Infineon Technologies AG.
** ============================================================================
*
*/

/** @file     smack_dand_mockup.c
 *  @brief    Smack demo firmware for smartphone app development
 *
 *  This example shall provide various functionalities to support development of a smartphone app, e.g.
 *  optical feedback of connection status. In further versions, it shall support development of an SDK
 *  on the smartphone as well as on the Smack which provides functionalities to build real world devices
 *  and apps.
 */

// standard libs
// included by core_cm0.h: #include <stdint.h>
#include "core_cm0.h"
#include <stdbool.h>
#include <string.h>
#include <stdlib.h>
#include <time.h>

// Smack ROM HAL
// #warning generated HAL in use
// #include "sys_tim_hal.h"

// Smack ROM lib
#include "rom_lib.h"

// Smack NVM lib
#include "inet_lib.h"
#include "sys_tim_lib.h"
#include "sys_tick_lib.h"
#include "shc_lib.h"

// Smack stepwise project
#include "version.h"
#include "settings.h"
#include "smack_dand_mockup.h"
#include "smack_dand_data.h"
#include "smack_dand_nvm.h"
#include "motor_sim.h"
#include "motor_stepwise_volt.h"
#include "motor_stepwise_time.h"
#include "motor_one_shot.h"

#include "util.h"
#include "my_nvm.h"
#include "sensor_util.h"

// get some ROM insights
#define currstate (*(NFC_State_enum_t *)0x02014e)
#define mailbox (*(Mailbox_t *)0x02000c)

// #undef WAIT_ABOUT_1MS
// #define WAIT_ABOUT_1MS  (XTAL / 1000U)          // calculated from oscillator; more exact than rough estimate
// #define ms2ticks(ms)    ((ms) * WAIT_ABOUT_1MS)       // -> sys tick lib

/** The functions which drive the motor are using timers in a different manner to estimate if the operationis completed.
 *  The timer channels to be used are defined here.
 */
#define TIMER_SINGLE 1 // timer # used for single shot delays
// #define TIMER_CLOCK     5       // timer # configured together with the preceeding timer as a free running 32 bit timer to be used for timing measurements

// interrupt number used in system timer calls
#define SYSTIM_IRQ 9

#define TIMER_SINGLE 1 // timer # used for single shot delays
#define TIMER_CLOCK 5  // timer # configured together with the preceeding timer as a free running 32 bit timer to be used for timing measurements

//-------------------------------------------------------------
// globals/statics

typedef enum
{
  motor_off = 0,
  motor_on,
  motor_reverse,
} motor_control_t;

static const aes_block_t aes_default_key =
    {
        {0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f}};

// process parameters
// static uint16_t threshold_low = (VCCHB_CLAMP_RAW * 2U / 3U);   // unused variable
// static uint16_t threshold_high = VCCHB_CLAMP_RAW;              // unused variable

static uint32_t main_loop_cnt;

// forward declaration...
static uint32_t timer_get32(void);

const smack_motor_services_t smack_motor_services =
    {
        .timer_get = timer_get32,
};

static struct
{
  smack_motor_control_status_t *status;
  const smack_motor_control_t *control;
} motor;

//-------------------------------------------------------------
// prototypes

static void motor_control(motor_control_t command);
static void lock_state_machine(void);
static void motor_status_start_operating(void);
static void config_activate(void);
static bool motor_select(uint8_t method);

//-------------------------------------------------------------
// Main loop design:
//
// The main loop incorporates one or more state machines which are controlled by external events (e.g. user interaction
// through smartphone) or process data (timers, voltages,...). It is executed in standard context and is the sole
// admnistrator of state structures.
//
// Communication with smartphone is done through the Dandelion handler, e.g. in interrupt context. Data exchange with
// main loop is done through flags and data structures.
//
// Main loop and Dandelion handler are running concurrently, no locking is in use. Flags and data structures have to
// be designed in a way that access is atomic. For most data type, a one-way messaging direction shall be defnied, and
// for complex data structures, a kind of mailbox design has to be considered in order to ensure data integrity.
//
// A concept of locking or critical sections is not used here because it requires to perform actions from inside the
// data exchange library, e.g. increases the time spent in interrupt context inside the Dandelion handler.

//-------------------------------------------------------------

/** _nvm_start() is the main() routine of the application code:
 * - when building the image (rom or ram), it is called by Reset_Handler() (see startup_smack.c) after SystemInit().
 * - when building and running the unit tests, it is not called, as far as I know, it is not subject to unit testing.
 * - when building and running integration and/or system tests, it is called by sc_main() upon simulation start.
 * This is also the reason of why it is called _start() and not main(): The VP has a higher layer main() which
 * calls sc_main() and _start(). Having two main's will fail when linking the VP executable.
 *
 * @note _start() cannot be made static, it is referenced by startup_smack.c and also by the interface
 * to the Virtual Prototype. But linting does not know about such external references. The default
 * approach to solve is to add '//lint -e765'. Our linter is plain old, we don't get a new one thanks to
 * the application owner from IT and it has a bug which renders -e765 useless. The only option we had
 * is to suppress the warning in au_misra2_fixes.lnt
 *
 * @return nothing
 */

// prototypes
void _nvm_start(void);

#ifdef DBG_GPIO_SMACK_EXCHANGE
void smack_exchange_handler_wrapper(void)
{
  set_singlegpio_out(1, DBG_GPIO_SMACK_EXCHANGE);
  smack_exchange_handler();
  set_singlegpio_out(0, DBG_GPIO_SMACK_EXCHANGE);
}
#endif

// calculate temperature from sense unit raw value (0.1degC unit)
// Function commented out - unused
/*
static int16_t tempRaw2Rated(uint16_t raw)
{
  int32_t rated;

#if 0
    // product spec p.371:
    // 790mV @-25degC
    // 535mV @105degC
    // delta 255mV --> 100mV...1.7V (inv)
    // ADC: 0...1.8V
    //temp = ((int32_t)dummy2 - (100*4096/1800)) * ( ((105+25)*10) / 1600 ) * (1800/4096);
    rated = (int32_t)raw * 10 * 1800 / 4096;    // raw -> 0.1mV
    rated -= 10 * 100;                          // -100mV
    rated = rated * (105 + 25) / 1600;          // 0.1mV -> 0.1K
    rated -= 10 * 25;                           // -> 0.1degC
    __DMB();
#else
  // excel sheet from iShare:
  //  512digits @-25degC
  // 3705digits @+85degC
  rated = (((int32_t)raw - 512) * 10 * (85 + 25) / (3705 - 512)) - 10 * 25; // raw -> 0.1degC
  __DMB();
#endif
  return (int16_t)rated;
}
*/

// Function commented out - unused
/*
static uint8_t chargeRaw2Rated(uint16_t raw)
{
  uint16_t rc;
  // Try to calculate with one 16-bit division only, more or less...
  // "raw" is a 12-bit value, if aligned to the left and then divided, accuracy of calculation is 0.5% or better.
  raw = raw << 4;
  rc = (raw / (uint16_t)((uint32_t)((VCCHB_CLAMP_RAW << 4) / 100)));

  if (rc > 100)
  {
    rc = 100;
  }

  return (uint8_t)rc;
}
*/

static uint8_t sense_unit_users = 0;

void sense_lock(void)
{
  shc_init();

  if (sense_unit_users < UINT8_MAX)
  {
    sense_unit_users++;
  }
  else
  {
    dbgp("%s: overflow!\n", __func__);
  }

  // dbgp("%s: users=%u\n", __func__, sense_unit_users);
}

void sense_free(void)
{
  if (sense_unit_users)
  {
    sense_unit_users--;

    if (sense_unit_users == 0)
    {
      shc_close();
    }
  }
  else
  {
    dbgp(" % s: not locked!\n", __func__);
  }

  // dbgp("%s: users=%u\n", __func__, sense_unit_users);
}

typedef enum
{
  led_off = 0,
  led_dand_off,
  led_dand_on,
  led_motor_forward,
  led_motor_reverse,
  led_motor_off,
} led_control_t;
static bool led_dand;
static led_control_t led_motor;

void led_control(led_control_t command)
{
  switch (command)
  {
  case led_off:
  case led_dand_off:
    led_dand = false;
    break;

  case led_dand_on:
    led_dand = true;
    break;

  case led_motor_forward:
  case led_motor_reverse:
    led_motor = led_motor_forward;
    break;

  case led_motor_off:
    led_motor = led_motor_off;
    break;
  }
}

void led_show(void)
{
#ifdef GPIO_LED1
  static bool led_current;
  static uint32_t last_tick;
  uint32_t now;
  bool new;

  new = led_current;

  if (led_motor == led_motor_forward || led_motor == led_motor_reverse)
  {
    // flash
    now = sys_tim_cyclic_cascaded_get_combined(TIMER_CLOCK);

    if (now - last_tick >= ms2ticks(200))
    {
      last_tick = now;
      new = (led_current == 0);
    }
  }
  else if (led_dand)
  {
    new = true;
  }
  else
  {
    new = false;
  }

  if (new != led_current)
  {
    set_singlegpio_out(new, GPIO_LED1);
    led_current = new;
  }

#endif
}

extern int32_t nvm_trimm_alt(void);

/** @brief main function
 *  This function performs initialization and a background task after motor operation is completed.
 *  The motor operation is done in a dedicated function as configured by the user.
 */
void _nvm_start(void)
{
  // bool led_state;  // unused variable - commented out
  bool tmp;
  uint32_t now;
  const aes_block_t *paes;

  nvm_trimm_alt();

#ifdef DBG_GPIO_SMACK_START
  set_singlegpio_out(1, DBG_GPIO_SMACK_START);
  single_gpio_iocfg(true /*out_enable*/, false /*in_enable*/, true /*outtype*/, false /*pup*/, false /*pdown*/, DBG_GPIO_SMACK_START);
#endif
#ifdef DBG_GPIO_HARDFAULT
  set_singlegpio_out(0, DBG_GPIO_HARDFAULT);
  single_gpio_iocfg(true /*out_enable*/, false /*in_enable*/, true /*outtype*/, false /*pup*/, false /*pdown*/, DBG_GPIO_HARDFAULT);
#endif
#ifdef DBG_GPIO_SMACK_EXCHANGE
  set_singlegpio_out(0, DBG_GPIO_SMACK_EXCHANGE);
  single_gpio_iocfg(true /*out_enable*/, false /*in_enable*/, true /*outtype*/, false /*pup*/, false /*pdown*/, DBG_GPIO_SMACK_EXCHANGE);
#endif

#if !defined DEBUG || !DEBUG
  // disable UART
  configure_uart_irq(get_uart_irq(), 0, 4, false, false);
  set_uart_control(false, false, false);
#else
  debug_init();
#endif

  // setup GPIO
  // led_state = false;  // unused variable assignment - commented out
#ifdef GPIO_LED1
  set_singlegpio_out(0, GPIO_LED1);
  single_gpio_iocfg(true /*out_enable*/, false /*in_enable*/, true /*outtype*/, false /*pup*/, false /*pdown*/, GPIO_LED1);
  set_singlegpio_alt(GPIO_LED1, 0 /*ain_en*/, 0 /*outsel*/);
  set_singlegpio_out(0, GPIO_LED1);
  single_gpio_iocfg(true /*out_enable*/, false /*in_enable*/, true /*outtype*/, false /*pup*/, false /*pdown*/, GPIO_LED1);
#endif
#ifdef DBG_GPIO_SMACK_EXCHANGE
  set_singlegpio_out(0, DBG_GPIO_SMACK_EXCHANGE);
  single_gpio_iocfg(true /*out_enable*/, false /*in_enable*/, true /*outtype*/, false /*pup*/, false /*pdown*/, DBG_GPIO_SMACK_EXCHANGE);
#endif
  // SCUC_MODULE_EN__SYS_TIM_CLK_EN__SET(scuc_module_en__sys_tim_clk_en_en); // enable systim bus clock
  sys_tim_cyclic_cascaded(TIMER_CLOCK, 0xffff, 0xffff);

  main_loop_cnt = 0;

  vars_init();
  motor_control(motor_off);
  config_load();
  user_cfg_read();
  log_load(); // initialize pointers

  // Setup NFC data point exchange
  // The smack_exchange_handler() callback must be configured in the APARAM block
  smack_exchange_init(data_point_list, data_point_count);
  paes = &aes_default_key;

  motor_select(config.method);
  motor_func(motor.control, init);

  if (session.user_count > 0)
  {
    paes = &smartlock_user.key;
  }

  smack_exchange_key_set(paes);
#ifdef DBG_GPIO_SMACK_START
  set_singlegpio_out(0, DBG_GPIO_SMACK_START);
#endif

  while (true)
  {
    tmp = (NFC_PROT_DAND == currstate);
    led_control((tmp) ? led_dand_on : led_dand_off);
    //        if (tmp != led_state)
    //        {
    //            set_singlegpio_out(tmp, GPIO_LED1);
    //            led_state = tmp;
    //        }

    if ((main_loop_cnt & 0x01) == 0) // increment 100ms counter in mailbox
    {
      session.global_counter++;
      // mailbox.content[MAILBOX_FW] = session.global_counter;
    }
    else
    {
      if ((main_loop_cnt & 0xfe) == 0)
      {
        dbgp("rf=%u\n", session.rf_strength_raw);
      }
    }

    lock_state_machine();
    led_show();

    if (session.lock_key_write)
    {
      session.lock_key_write = false;
      user_cfg_write();
    }

    if (session.erase)
    {
      uint8_t erase = session.erase;
      session.erase = 0;
      __DMB();
      nvm_clear(erase);
    }

    if (session.config_write)
    {
      now = sys_tim_cyclic_cascaded_get_combined(TIMER_CLOCK);

      if (session.config_write_force || now - session.config_time > ms2ticks(800))
      {
        dbgp("cfg f: % u\n", session.config_write_force);
        session.config_write_force = session.config_write = session.config_time_set = false;
        __DMB();
        config_save();
        session.config_activate = true;
        session.config_activate_time = now;
        dbgp("cfg: written\n");
      }
    }

    if (session.config_activate)
    {
      now = sys_tim_cyclic_cascaded_get_combined(TIMER_CLOCK);

      if (now - session.config_activate_time > ms2ticks(150))
      {
        session.config_activate = false;
        config_activate();
      }
    }

    // sys_tim_singleshot_32(TIMER_SINGLE, 50U * wait_about_1ms, SYSTIM_IRQ);
    single_shot_systick(ms2ticks(10));
    main_loop_cnt++;

    debug_task();
  }
}

// quick and dirty hack...
uint16_t data_get_index(uint16_t datapoint)
{
  uint16_t index;

  for (index = 0; index < data_point_count; index++)
  {
    if (data_point_list[index].data_point_id == datapoint)
    {
      break;
    }
  }

  return index;
}

void data_rx(uint16_t datapoint)
{
#ifdef DBG_GPIO_SMACK_EXCHANGE
  set_singlegpio_out(1, DBG_GPIO_SMACK_EXCHANGE);
#endif

  switch (datapoint)
  {
  case X1811_LOG_SELECT:
    log_load_entry(log_if.select);
    break;

  case X0120_LOCK_CONTROL: // ignore command if not armed
    if (lock.control != lock.arm)
    {
      lock.control = 0;
    }
    else if ((lock.control & (lock_control_locked | lock_control_unlocked)) != 0)
    {
      motor_status_start_operating();
    }

    lock.arm = 0;
    break;

  case X1900_USER_SELECT:
    if (session.user_select_count == 0)
    {
      if (session.user_select == 1 || session.user_select == 254)
      {
        const aes_block_t *p;

        if (session.user_select == 254)
        {
          p = &config_device.su_key;
        }
        else if (session.user_count)
        {
          p = &smartlock_user.key;
        }
        else
        {
          p = &aes_default_key;
        }

        smack_exchange_key_set(p);
        session.user_selected = session.user_select;

        if (session.user_select_count < UINT16_MAX)
        {
          session.user_select_count++;
        }
      }
    }

#if defined DEBUG && DEBUG
    else
    {
      smack_exchange_key_set(&smartlock_user.key);
    }

#endif
    break;

  case X1902_LOCK_KEY:
  {
    aes_block_t tmp;
    uint64_t *p;
    smartlock_user.key = session.lock_key;
    memset(&session.lock_key, 0, sizeof(session.lock_key));
    tmp.w[2] = tmp.w[3] = 0;
    p = (void *)tmp.b;
    *p = htonll(lock.id);
    aes_load_key_ba(&smartlock_user.key);
    calc_aes_ba(&session.lock_key_check, &tmp, encrypt);
    smack_exchange_key_restore();
    session.lock_key_received = true;
  }
  break;

  case X1901_LOCK_KEY_STORE:

    // TODO: check if key was received, check command pattern
    if (session.lock_key_store == LOCK_KEY_WRITE_COMMAND
        // #if (FIRMWARE_VERSION_BUILD & 1) == 0
        //                     || session.lock_key_store == 0x30783832
        // #endif
    )
    {
      session.lock_key_write = true;
      session.lock_key_received = false;

      if (session.user_count == 0)
      {
        session.user_count = 1;
      }

      memset(&session.lock_key_check, 0, sizeof(session.lock_key_check));
    }

    break;

  case X1003_CONFIG_VOLT_VSTOP:
  case X1005_CONFIG_TIME_TOFF:
  case X1006_CONFIG_SINGLE_VSTART:
    session.config_write_force = true;

  // fall through...
  case X1001_CONFIG_VCLAMP:
  case X1007_CONFIG_TIME_TOTAL:
  case X1000_CONFIG_METHOD:
  case X1002_CONFIG_VOLT_VSTART:
  case X1004_CONFIG_TIME_TON:
    session.config_write = true;

    if (!session.config_time_set)
    {
      session.config_time = sys_tim_cyclic_cascaded_get_combined(TIMER_CLOCK);
      session.config_time_set = true;
    }

    break;

  case XF002_SCRATCH8:
    session.erase = scratch8;
    break;
  }

  if (datapoint != X0120_LOCK_CONTROL && datapoint != X0121_LOCK_ARM)
  {
    lock.arm = 0;
  }

  debug_log_de(0, datapoint, data_get_index(datapoint));
#ifdef DBG_GPIO_SMACK_EXCHANGE
  set_singlegpio_out(0, DBG_GPIO_SMACK_EXCHANGE);
#endif
}

void data_tx(uint16_t datapoint)
{
#ifdef DBG_GPIO_SMACK_EXCHANGE
  set_singlegpio_out(1, DBG_GPIO_SMACK_EXCHANGE);
#endif

  switch (datapoint)
  {
  case X0010_CHARGE_RAW:
    if (motor.status)
    {
      lock.charge_raw = motor.status->charge;
    }
    else
    {
      lock.charge_raw = 0;
    }

    break;

  case X0011_CHARGE_RAW_THRESHOLD:
    if (motor.status)
    {
      lock.charge_raw_threshold = motor.status->charge_threshold;
    }
    else
    {
      lock.charge_raw_threshold = VCCHB_CLAMP_RAW;
    }

    break;

  case X0012_CHARGE_PERCENT:
    if (motor.status && motor.status->charge_threshold)
    {
      lock.charge_percent = (motor.status->charge * 100U) / motor.status->charge_threshold;

      if (lock.charge_percent > 100)
      {
        lock.charge_percent = 100;
      }
    }
    else
    {
      lock.charge_percent = 0;
    }

    break;

  case X0021_LOCK_CONTROL_PROGRESS:
    if (motor.status && motor.status->progress_threshold)
    {
      if (motor.status->progress <= UINT32_MAX / 100U)
      {
        lock.control_progress = (motor.status->progress * 100U) / motor.status->progress_threshold;
      }
      else
      {
        float control_progress = ((float)motor.status->progress / (float)motor.status->progress_threshold) * 100U;
        lock.control_progress = (uint8_t)control_progress;
      }

      if (lock.control_progress > 100)
      {
        lock.control_progress = 100;
      }
    }
    else
    {
      lock.control_progress = 0;
    }

    break;
  }

  debug_log_de(1, datapoint, data_get_index(datapoint));
#ifdef DBG_GPIO_SMACK_EXCHANGE
  set_singlegpio_out(0, DBG_GPIO_SMACK_EXCHANGE);
#endif
}

// convert config for motor control into local units (e.g. convert ms to ticks) and write to appropriate variables
static void config_activate(void)
{
  bool method_changed = false;
  dbgp("%s(): todo\n", __func__);
  method_changed = (session.method != config.method);

  if (method_changed)
  {
    motor_func(motor.control, exit);
  }

  motor_select(config.method);

  if (method_changed)
  {
    motor_func(motor.control, init);
  }

  motor_func(motor.control, config);
}

static bool motor_select(uint8_t method)
{
  bool rc = false;

  switch (method)
  {
  case motor_stepwise_voltage:
    session.method = motor_stepwise_voltage;
    motor.status = &motor_stepwise_volt_status;
    motor.control = &motor_stepwise_volt_control;
    motor.status->param1 = config.volt.v_start;
    motor.status->param2 = config.volt.v_stop;
    rc = true;
    break;

  case motor_stepwise_timer:
    session.method = motor_stepwise_timer;
    motor.status = &motor_stepwise_time_status;
    motor.control = &motor_stepwise_time_control;
    motor.status->param1 = ms2ticks(config.time.t_on);
    motor.status->param2 = ms2ticks(config.time.t_off);
    rc = true;
    break;

  case motor_single:
    session.method = motor_single;
    motor.status = &motor_one_shot_status;
    motor.control = &motor_one_shot_control;
    motor.status->param1 = config.single.v_start;
    rc = true;
    break;

  default:
    session.method = motor_simulation;
    motor.status = &motor_sim_status;
    motor.control = &motor_sim_control;
    motor.status->param1 = config.volt.v_start;
    motor.status->param2 = config.volt.v_stop;
  }

  if (motor.status)
  {
    motor.status->v_clamp = config.v_clamp;
    motor.status->t_total = config.t_total;
  }

  return rc;
}

static void motor_control(motor_control_t command)
{
  static motor_control_t last_command;

  if (command != last_command)
  {
    switch (command)
    {
    case motor_on:
      led_control(led_motor_forward);
      set_hb_switch(false, false, false, false);
      set_hb_switch(true, false, false, true);
      break;

    case motor_reverse:
      led_control(led_motor_reverse);
      set_hb_switch(false, false, false, false);
      set_hb_switch(false, true, true, false);
      break;

    case motor_off:
    default:
      led_control(led_motor_off);

      switch (last_command)
      {
      case motor_on:
        set_hb_switch(true, false, false, false);
        break;

      case motor_reverse:
        set_hb_switch(false, true, false, false);
        set_hb_switch(false, false, false, false);
        set_hb_switch(true, false, false, false);
        break;

      default:
        set_hb_switch(false, false, false, false);
        set_hb_switch(true, false, false, false);
        break;
      }

      break;
    }

    last_command = command;
  }
}

void motor_status_set(uint32_t status)
{
  switch (status)
  {
  case lock_control_locked:
    lock.control_status = (lock.control_status & ~SMACK_STATUS_MOTOR_MASK) | lock_control_locked;
    session.flags = (session.flags & ~SMACK_FLAGS_MOTOR_MASK) | smack_flags_locked;
    break;

  case lock_control_unlocked:
    lock.control_status = (lock.control_status & ~SMACK_STATUS_MOTOR_MASK) | lock_control_unlocked;
    session.flags = (session.flags & ~SMACK_FLAGS_MOTOR_MASK) | smack_flags_unlocked;
    break;

  case lock_control_operating:
    lock.control_status = (lock.control_status & ~SMACK_STATUS_MOTOR_MASK) | lock_control_operating;
    // session.flags = (session.flags & ~SMACK_FLAGS_MOTOR_MASK) | smack_flags_??;
    break;
  }
}

static void motor_status_start_operating(void)
{
  if ((lock.control_status & lock_control_operating) != 0)
  {
    lock.control_progress = 0;
    motor_status_set(lock_control_operating);
  }
}

__attribute__((noinline)) static void lock_state_machine(void)
{
  static smack_motor_status_t last_status = motor_status_unknown;
  bool close, open, stop;

  open = close = stop = false;
  __DMB();

  // should be enclosed in critical section
  //    if (lock_control_stop /*|| (lock_control & ??)*/)
  //    {
  //        stop = true;
  //    }
  //    else
  if (/*lock_control_lock ||*/ (lock.control & lock_control_locked))
  {
    close = true;
    dbgp("lock: close\n");
  }
  else if (/*lock_control_unlock ||*/ (lock.control & lock_control_unlocked))
  {
    dbgp("lock: open\n");
    open = true;
  }

  // TODOs:
  // - start lock operation only when cap is fully charged
  // - add overall timeout

  //    lock_control_lock = lock_control_unlock = lock_control_stop = false;
  lock.control &= ~(lock_control_locked | lock_control_unlocked);
  __DMB();

  if (motor.status->status != motor_status_moving)
  {
    if (close)
    {
      motor.control->control(motor_ctrl_reverse);
      log_write(lock_control_locked);
    }
    else if (open)
    {
      motor.control->control(motor_ctrl_on);
      log_write(lock_control_unlocked);
    }
  }

  motor.control->task();

  if (last_status != motor.status->status)
  {
    switch (motor.status->status)
    {
    case motor_status_moving:
      if (close)
      {
        led_control(led_motor_reverse);
      }
      else if (open)
      {
        led_control(led_motor_forward);
      }

      break;

    default:
      if (!open && !close)
      {
        led_control(led_motor_off);
      }
    }

    last_status = motor.status->status;
  }
}

static uint32_t timer_get32(void)
{
  return sys_tim_cyclic_cascaded_get_combined(TIMER_CLOCK);
}

void hard_fault_delay(void)
{
#ifndef DBG_GPIO_HARDFAULT
  uint32_t delay = 30000000;

  while (delay--)
  {
    __NOP();
  }

#else
  uint32_t delay = 30000;
  uint32_t i;

  set_singlegpio_out(1, DBG_GPIO_HARDFAULT);
  single_gpio_iocfg(true /*out_enable*/, false /*in_enable*/, true /*outtype*/, false /*pup*/, false /*pdown*/, DBG_GPIO_HARDFAULT);
#if defined DEBUG && DEBUG
  static const char *msg = "\n!!! HARDFAUL !!!\n";
  uart_send((const uint8_t *)msg, sizeof(msg) - 1);
#endif

  while (delay--)
  {
    for (i = 0; i < 500; i++)
    {
      __NOP();
    }

    set_singlegpio_out(0, DBG_GPIO_HARDFAULT);

    for (i = 0; i < 500; i++)
    {
      __NOP();
    }

    set_singlegpio_out(1, DBG_GPIO_HARDFAULT);
  }

#endif
}



uint32_t bcd(uint32_t value)
{
  uint32_t rc = 0;
  uint32_t d;
  uint8_t shift = 0;

  while (value && shift < 32)
  {
    d = value % 10;
    value /= 10;
    rc |= d << shift;
    shift += 4;
  }

  return rc;
}

void dump_config(void)
{
  Mailbox_t *p;

  p = get_mailbox_address();
  int t;
  for (t = 0; t <= 15; t++)
  {
    p->content[t] = 0x0;
  }
  p->content[0] = config.method;
  p->content[1] = config.v_clamp;
  p->content[2] = config.t_total;
  p->content[4] = config.volt.v_start;
  p->content[5] = config.volt.v_stop;
  p->content[8] = config.time.t_on;
  p->content[9] = config.time.t_off;
  p->content[12] = config.single.v_start;
  p->content[15] = main_loop_cnt;
}

void dump_config_bcd(void)
{
  Mailbox_t *p;

  p = get_mailbox_address();
  int t;
  for (t = 0; t <= 15; t++)
  {
    p->content[t] = 0x0;
  }
  p->content[0] = bcd(config.method);
  p->content[1] = bcd(config.v_clamp);
  p->content[2] = bcd(config.t_total);
  p->content[4] = bcd(config.volt.v_start);
  p->content[5] = bcd(config.volt.v_stop);
  p->content[8] = bcd(config.time.t_on);
  p->content[9] = bcd(config.time.t_off);
  p->content[12] = bcd(config.single.v_start);
  p->content[15] = bcd(main_loop_cnt);
}

void nvm_erase_content(void)
{
  static const char user[] = "del userkey";
  static const char config[] = "del config";
  static const char log[] = "del log";
  typedef union
  {
    char c[4];
    uint32_t l[1];
  } response_t;
  static const response_t log_r = {{'L', 'O', 'G', '\0'}};
  static const response_t user_r = {{'U', 'S', 'E', 'R'}};
  static const response_t config_r = {{'C', 'F', 'G', '\0'}};
  Mailbox_t *p;
  uint32_t buf[3];
  int i;

  p = get_mailbox_address();

  for (i = 0; i < sizeof(buf) / sizeof(buf[0]); i++)
  {
    buf[i] = ntohl(p->content[i + 1]);
  }

  memset(p->content, 0, 20);

  if (strncmp((char *)buf, log, sizeof(log) - 1) == 0)
  {
    session.erase |= 0x02;
    p->content[0] = htonl(log_r.l[0]);
  }
  else if (strncmp((char *)buf, config, sizeof(config) - 1) == 0)
  {
    session.erase |= 0x04;
    p->content[0] = htonl(config_r.l[0]);
  }
  else if (strncmp((char *)buf, user, sizeof(user) - 1) == 0)
  {
    session.erase |= 0x01;
    p->content[0] = htonl(user_r.l[0]);
  }
}


